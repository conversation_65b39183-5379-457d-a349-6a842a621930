import { useState, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { api } from '../lib/api'
import ServerList from '../components/ServerList'
import AddServerModal from '../components/AddServerModal'
import EditServerModal from '../components/EditServerModal'
import CommandModal from '../components/CommandModal'
import ServerGroupManager from '../components/ServerGroupManager'
import ServerGroupFilter from '../components/ServerGroupFilter'
import CommandAutocomplete from '../components/CommandAutocomplete'
import ServerBackupModal from '../components/ServerBackupModal'
import { SSHServer, Command } from '../types/server'
import { CommandSearchResult } from '../services/api'
import { PlusCircle, Database, FolderPlus, Search, X, Upload } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

export function Servers() {
  const { user } = useAuth()
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isCommandModalOpen, setIsCommandModalOpen] = useState(false)
  const [isGroupManagerOpen, setIsGroupManagerOpen] = useState(false)
  const [isImportModalOpen, setIsImportModalOpen] = useState(false)
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null)
  const [selectedServer, setSelectedServer] = useState<SSHServer & { commands: Command[] } | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [commandSearchTerm, setCommandSearchTerm] = useState('')

  const { data: servers, refetch } = useQuery<(SSHServer & { commands: Command[] })[]>({
    queryKey: ['servers'],
    queryFn: async () => {
      const response = await api.get('/api/servers')
      const data = response.data.servers || []
      return data.map((server: SSHServer) => ({
        ...server,
        commands: server.commands || []
      }))
    },
  })

  // Filtrar servidores por grupo selecionado e termo de pesquisa
  const filteredServers = useMemo(() => {
    if (!servers) return []

    let filtered = servers

    // Filtrar por grupo
    if (selectedGroupId) {
      filtered = filtered.filter(server =>
        server.groupMembers?.some(member => member.group.id === selectedGroupId)
      )
    }

    // Filtrar por termo de pesquisa
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim()
      filtered = filtered.filter(server =>
        server.name.toLowerCase().includes(term) ||
        server.host.toLowerCase().includes(term) ||
        server.username.toLowerCase().includes(term) ||
        server.deviceType.toLowerCase().includes(term)
      )
    }

    return filtered
  }, [servers, selectedGroupId, searchTerm])

  function handleAddServer() {
    setIsAddModalOpen(true)
  }

  function handleEditServer(server: SSHServer & { commands: Command[] }) {
    setSelectedServer(server)
    setIsEditModalOpen(true)
  }

  function handleExecuteCommand(server: SSHServer & { commands: Command[] }) {
    setSelectedServer(server)
    setIsCommandModalOpen(true)
  }

  function handleDeleteServer(server: SSHServer & { commands: Command[] }) {
    if (confirm('Tem certeza que deseja excluir este servidor?')) {
      api.delete(`/api/servers/${server.id}`).then(() => {
        refetch()
      })
    }
  }

  function handleCommandSelect(command: CommandSearchResult) {
    // Encontrar o servidor correspondente
    const server = servers?.find(s => s.id === command.serverId)
    if (server) {
      setSelectedServer(server)
      setCommandSearchTerm(command.name) // Definir o termo de pesquisa para filtrar o comando
      setIsCommandModalOpen(true)
    }
  }

  return (
    <div className="py-4 sm:py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Cabeçalho principal */}
        <div className="mb-6">
          {/* Linha do título e botões principais */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
            <div className="flex items-center gap-3">
              <Database className="h-7 w-7 text-primary-500" />
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Servidores</h1>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={() => setIsImportModalOpen(true)}
                className="bg-orange-600 text-white hover:bg-orange-700 px-5 py-2.5 rounded-lg flex items-center justify-center gap-2 font-medium transition-all duration-200 hover:shadow-md w-full sm:w-auto"
              >
                <Upload className="h-5 w-5" />
                Importar Servidor
              </button>
              <button
                onClick={() => setIsGroupManagerOpen(true)}
                className="bg-blue-600 text-white hover:bg-blue-700 px-5 py-2.5 rounded-lg flex items-center justify-center gap-2 font-medium transition-all duration-200 hover:shadow-md w-full sm:w-auto"
              >
                <FolderPlus className="h-5 w-5" />
                Gerenciar Grupos
              </button>
              {user?.role === 'ADMIN' && (
                <button
                  onClick={handleAddServer}
                  className="bg-blue-600 text-white hover:bg-blue-700 px-5 py-2.5 rounded-lg flex items-center justify-center gap-2 font-medium transition-all duration-200 hover:shadow-md w-full sm:w-auto"
                >
                  <PlusCircle className="h-5 w-5" />
                  Adicionar Servidor
                </button>
              )}
            </div>
          </div>

          {/* Linha de filtros e pesquisa */}
          <div className="p-4 bg-gray-50 rounded-xl border border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center gap-4">
              {/* Filtro por grupos - só aparece se houver grupos */}
              <ServerGroupFilter
                selectedGroupId={selectedGroupId}
                onGroupSelect={setSelectedGroupId}
              />

              <div className="flex flex-col md:flex-row gap-4 lg:ml-auto">
                {/* Pesquisa de comandos */}
                <CommandAutocomplete
                  onCommandSelect={handleCommandSelect}
                  placeholder="Pesquisar comandos..."
                  className="w-full md:w-80"
                />

                {/* Barra de pesquisa de servidores */}
                <div className="relative w-full md:w-80">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Pesquisar servidores..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-all duration-200"
                  />
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm('')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <ServerList
          servers={filteredServers}
          onServerUpdated={refetch}
        />

        {isAddModalOpen && (
          <AddServerModal
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
            onServerAdded={refetch}
          />
        )}

        {isEditModalOpen && selectedServer && (
          <EditServerModal
            isOpen={isEditModalOpen}
            server={selectedServer}
            onClose={() => setIsEditModalOpen(false)}
            onServerUpdated={refetch}
          />
        )}

        {isCommandModalOpen && selectedServer && (
          <CommandModal
            isOpen={isCommandModalOpen}
            server={selectedServer}
            onClose={() => {
              setIsCommandModalOpen(false)
              setCommandSearchTerm('') // Limpar termo de pesquisa ao fechar
            }}
            initialSearchTerm={commandSearchTerm}
            autoExecuteCommand={!!commandSearchTerm}
          />
        )}

        <ServerGroupManager
          isOpen={isGroupManagerOpen}
          onClose={() => setIsGroupManagerOpen(false)}
        />

        <ServerBackupModal
          isOpen={isImportModalOpen}
          onClose={() => setIsImportModalOpen(false)}
          onServerUpdated={refetch}
        />
      </div>
    </div>
  )
}